<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<input id="dailySaleSetID" type="hidden"  data-sale="0" data-set="0">
<input id="isAllOnCod" type="hidden" value="">
<!--分页-->
<div class="col-xs-12 p0 mTop10" style="text-align:right;min-height:35px;">
                <ul id="upPage" class="pageDiv" style="margin:0 0 4px;"></ul>
    </div>
<div class="col-xs-12 p0">
	<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<style>
    .click-expand{width: 32px;height: 28px;position: absolute;left: 0;top: 0;z-index: 1;}
</style>
<form id="printJhForm" action="" method="POST" target="_blank">
    <input id="pageNo" type="hidden" value="1">
    <input id="totalPage" type="hidden" value="1">
    <input id="pageSize" type="hidden" value="300">
    <input id="totalSize" type="hidden" value="8">
    <input id="orderIdsStr" type="hidden" value="138220103904402488;138220103903910862;138220103903911010;138220103903911148;138220103903911256;138220103902559598;138220103902329518;138220103902329600">
    <button class="hide" type="button" id="printJhBtn1" onclick="batchPrintPackageHtml(1)">print</button>
    <button class="hide" type="button" id="printJhBtn2" onclick="PrintJhAll(1)">print</button>
    <button class="hide" type="button" id="printJhBtn3" onclick="PrintJhAll(2)">print</button>
    <button class="hide" type="button" id="printJhBtn4" onclick="PrintJhAll(3)">print</button>
    <button class="hide" type="button" id="printJhBtn6" onclick="PrintJhAll(3, 1)">print</button>
    <button class="hide" type="button" id="printJhBtn5" onclick="batchPrintPackageHtml(1,'','',1)">print</button>
    <input type="hidden" id="isDesc" name="isDesc" value=""/>
    <input type="hidden" id="authId" name="authId" value=""/>
    <input type="hidden" id="packageIds" name="packageIds" value=""/>
    <input type="hidden" id="orderField" name="orderField" value=""/>
    <input type="hidden" id="stockProductNum" value="0">
    <table class="myj-table" id="orderListTable" cid="productList">
        <thead>
        <tr>
            <th class="p0-imp minW190">
                <input class="pull-left m-left10" id="selectAll" name="curPage"
                       type="checkbox" selIptId="selectAll"
                       onclick="showSelCheckboxNum(this, 'order');"/>&nbsp;&nbsp;商品信息
            </th>
            <th class="minW90">订单金额</th>
            <th class="minW135">收件人「国家/地区」</th>
            <th class="minW110-imp w180-imp">订单号</th>
            <th class="minW160">时间</th>
            <th>状态</th>
            <th class="border-right2 w70 minW70 maxW70">操作</th>
        </tr>
        </thead>
        <tbody class="xianshishujudate">
        <input type="hidden" id="profitPermission" value="1" /><tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103904402488" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103904402488" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="6839796" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103904402488" data-createtime="2025-08-19 15:09:22.0"
                                   data-packageNumber="XM1XC77031383"/>
                            <input type="hidden" id="trackingNumber_138220103904402488" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103904402488');">XM1XC77031383</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103904402488"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：溶溶-半托管」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103904402488"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103904402488"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/3efcc815e3784e759ac3cc4cb00cd11c-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/3efcc815e3784e759ac3cc4cb00cd11c-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">0343675059</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanGrey" >1</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：44815074638</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="溶溶-半托管"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103904402488_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103904402488"></span>
                                            <span id="unit_138220103904402488"></span>
                                            <span class="f13" id="profit_138220103904402488">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103904402488">
                                            <span id="pShowTypeVal_138220103904402488"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103904402488" type="hidden" value="PO-211-19138366403192551">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103904402488');">PO-211-19138366403192551</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：0343675059<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-19 13:48</div>
                                <div>付款：2025-08-19 13:48</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103904402488">
                                        <script>addTimer("timeLeft_138220103904402488", 941863,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103904402488');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103904402488');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103904402488');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103903910862" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103903910862" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="6839796" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103903910862" data-createtime="2025-08-19 13:46:25.0"
                                   data-packageNumber="XM1XC77031361"/>
                            <input type="hidden" id="trackingNumber_138220103903910862" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103903910862');">XM1XC77031361</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103903910862"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：溶溶-半托管」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103903910862"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103903910862"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/9046904b229543a89e09fa1821cfc0d8-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/9046904b229543a89e09fa1821cfc0d8-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">3780468646</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanGrey" >1</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：93683989497</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="溶溶-半托管"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103903910862_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103903910862"></span>
                                            <span id="unit_138220103903910862"></span>
                                            <span class="f13" id="profit_138220103903910862">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103903910862">
                                            <span id="pShowTypeVal_138220103903910862"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103903910862" type="hidden" value="PO-211-18706302198392020">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103903910862');">PO-211-18706302198392020</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：3780468646<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-19 13:02</div>
                                <div>付款：2025-08-19 13:02</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103903910862">
                                        <script>addTimer("timeLeft_138220103903910862", 1278810,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103903910862');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103903910862');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103903910862');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103903911010" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103903911010" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="6839796" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103903911010" data-createtime="2025-08-19 13:46:26.0"
                                   data-packageNumber="XM1XC77031362"/>
                            <input type="hidden" id="trackingNumber_138220103903911010" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103903911010');">XM1XC77031362</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103903911010"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：溶溶-半托管」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103903911010"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103903911010"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/3dfaa82e715e4e0e8d9d4bf0b15f0598-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/3dfaa82e715e4e0e8d9d4bf0b15f0598-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">R223572354</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanGrey" >1</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：10676607051</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/b331b2fe8645454d94986a92f8eceac2-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/b331b2fe8645454d94986a92f8eceac2-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">R881569421</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanGrey" >1</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：67156568439</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="溶溶-半托管"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103903911010_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103903911010"></span>
                                            <span id="unit_138220103903911010"></span>
                                            <span class="f13" id="profit_138220103903911010">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103903911010">
                                            <span id="pShowTypeVal_138220103903911010"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103903911010" type="hidden" value="PO-211-19034926372473935">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103903911010');">PO-211-19034926372473935</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：R223572354、R881569421<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-19 12:38</div>
                                <div>付款：2025-08-19 12:38</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103903911010">
                                        <script>addTimer("timeLeft_138220103903911010", 944010,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103903911010');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103903911010');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103903911010');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103903911148" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103903911148" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="6839796" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103903911148" data-createtime="2025-08-19 13:46:27.0"
                                   data-packageNumber="XM1XC77031363"/>
                            <input type="hidden" id="trackingNumber_138220103903911148" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103903911148');">XM1XC77031363</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103903911148"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：溶溶-半托管」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103903911148"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103903911148"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/f6d85636892b4c12b0bc1fe31c13410c-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/f6d85636892b4c12b0bc1fe31c13410c-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">5233042112</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanGrey" >1</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：15654738695</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="溶溶-半托管"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103903911148_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103903911148"></span>
                                            <span id="unit_138220103903911148"></span>
                                            <span class="f13" id="profit_138220103903911148">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103903911148">
                                            <span id="pShowTypeVal_138220103903911148"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103903911148" type="hidden" value="PO-211-19114208041592407">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103903911148');">PO-211-19114208041592407</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：5233042112<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-19 12:38</div>
                                <div>付款：2025-08-19 12:38</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103903911148">
                                        <script>addTimer("timeLeft_138220103903911148", 938262,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103903911148');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103903911148');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103903911148');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103903911256" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103903911256" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="6839796" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103903911256" data-createtime="2025-08-19 13:46:28.0"
                                   data-packageNumber="XM1XC77031364"/>
                            <input type="hidden" id="trackingNumber_138220103903911256" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103903911256');">XM1XC77031364</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103903911256"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：溶溶-半托管」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103903911256"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103903911256"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/ee9d613bb36e4542ad5925ffad7fb5ba-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/ee9d613bb36e4542ad5925ffad7fb5ba-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">TT10847073</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanRed">12</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：42178658102</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="溶溶-半托管"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103903911256_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103903911256"></span>
                                            <span id="unit_138220103903911256"></span>
                                            <span class="f13" id="profit_138220103903911256">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103903911256">
                                            <span id="pShowTypeVal_138220103903911256"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103903911256" type="hidden" value="PO-211-18602958305911356">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103903911256');">PO-211-18602958305911356</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：TT10847073<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-19 12:29</div>
                                <div>付款：2025-08-19 12:29</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103903911256">
                                        <script>addTimer("timeLeft_138220103903911256", 1278810,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103903911256');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103903911256');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103903911256');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103902559598" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103902559598" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="6839796" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103902559598" data-createtime="2025-08-19 10:32:16.0"
                                   data-packageNumber="XM1XC77031336"/>
                            <input type="hidden" id="trackingNumber_138220103902559598" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103902559598');">XM1XC77031336</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103902559598"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：溶溶-半托管」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103902559598"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103902559598"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/b9dd0a6bc08e4d1b9817bbc1b13bf049-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/b9dd0a6bc08e4d1b9817bbc1b13bf049-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">5430035265</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanGrey" >1</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：37882218913</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="溶溶-半托管"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103902559598_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103902559598"></span>
                                            <span id="unit_138220103902559598"></span>
                                            <span class="f13" id="profit_138220103902559598">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103902559598">
                                            <span id="pShowTypeVal_138220103902559598"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103902559598" type="hidden" value="PO-211-19015633377913792">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103902559598');">PO-211-19015633377913792</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：5430035265<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-19 10:11</div>
                                <div>付款：2025-08-19 10:11</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103902559598">
                                        <script>addTimer("timeLeft_138220103902559598", 936810,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103902559598');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103902559598');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103902559598');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103902329518" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103902329518" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="6839796" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103902329518" data-createtime="2025-08-19 10:01:52.0"
                                   data-packageNumber="XM1XC77031296"/>
                            <input type="hidden" id="trackingNumber_138220103902329518" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103902329518');">XM1XC77031296</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103902329518"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：溶溶-半托管」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103902329518"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103902329518"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/b9dd0a6bc08e4d1b9817bbc1b13bf049-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/b9dd0a6bc08e4d1b9817bbc1b13bf049-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">5430035265</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanGrey" >1</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：37882218913</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="溶溶-半托管"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103902329518_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103902329518"></span>
                                            <span id="unit_138220103902329518"></span>
                                            <span class="f13" id="profit_138220103902329518">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103902329518">
                                            <span id="pShowTypeVal_138220103902329518"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103902329518" type="hidden" value="PO-211-18817387029110590">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103902329518');">PO-211-18817387029110590</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：5430035265<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-19 09:46</div>
                                <div>付款：2025-08-19 09:46</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103902329518">
                                        <script>addTimer("timeLeft_138220103902329518", 937537,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103902329518');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103902329518');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103902329518');">详情</a></td>
                            </tr>
                    <tr class="goodsId" data-custom-mark="0,0,0,0,0,0,0,0,0,0">
                        <td colspan="2" class="relative orderListTitFirTd parent">
                            <div class="click-expand expand_scope"></div>
                            <input class="input1" type="checkbox" name="packageId" datename="showdate" onclick="showSelCheckboxNum(this, 'order');" value="138220103902329600" data-commit="uncommit" />
                            <input type="hidden" id="dxmAuthId138220103902329600" value="0" data-platform="pddkj"
                                   data-globalcollection="0"
                                   data-buyerSelectProvider=""
                                   data-shopid="6839796" data-storageid="7677986" data-agentid="0"
                                   data-country="US" data-orderId="138220103902329600" data-createtime="2025-08-19 10:01:53.0"
                                   data-packageNumber="XM1XC77031297"/>
                            <input type="hidden" id="trackingNumber_138220103902329600" value="">
                            <a href="javascript:" class="limingcentUrlpic" onclick="dxmPackageDetail('138220103902329600');">XM1XC77031297</a>
                                <div class="custom-order-mark custom-sign-myj customOrderMark" uid="custom-sign-myj" data-orderid="138220103902329600"></div>
                            </td>
                        <td class="f-right p-right30" colspan="1">
                                            <span>支付:
                                    非货到付款(PPD)</span>
                            </td>
                        <td colspan="2">
                            </td>
                        <td class="f-right" colspan="5">
                            <span class="order-form-source">「Temu：溶溶-半托管」</span>
                        </td>
                    </tr>
                    <tr class="orderId_138220103902329600"
                            data-applyTrackNumStatus=""
                            data-pddtag=""
                            data-isHaCnz="0"
                            data-platform="pddkj"
                            data-agentName=""
                            data-orderPrice="0"
                            data-orderid="138220103902329600"
                        >
                            <td class="p0-imp">
                                <table class="tableIn">
                                    <tr class="pairProInfo">
                                            <td class="p-right0 w60 f-center">
                                                <div class="listImgOut m-left0 make-pair-box make-pair-div">
                                                    <div class="listImgIn"
                                                         data-type="0"
                                                         data-hasProductId = "0">
                                                            <img class="imgCss lazy pairProInfoImg" src=""
                                                                         data-original="https://img.cdnfe.com/product/open/6062fd76347b430a924e070b39d17f62-goods.jpeg"
                                                                         data-order="https://img.cdnfe.com/product/open/6062fd76347b430a924e070b39d17f62-goods.jpeg" referrerpolicy="no-referrer" />
                                                                </div>
                                                    </div>
                                                <input id="orderSourceUrlType" type="hidden" value="1">
                                                    </td>
                                            <td class="p-right0">
                                                <div>
                                                    <p>
                                                        <span class="limingcentUrlpicson">
                                                            <a class="pairProInfoSku productUrl" href=""
                                                                       target="_blank">6795236809</a>
                                                                </span>
                                                        &nbsp;x&nbsp;
                                                        <span class="circularSpanGrey" >1</span>
                                                        <span class="squareSpan bg-dark-blue delayTips hh" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>Y2模式中国直邮预售产品</div><div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div>">Y2</span>
                                                        </p>
                                                    <p>CNY&nbsp;0</p>
                                                    <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    SKUID：91358478875</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Variants：A</span>
                                                            </p>
                                                        <p class="pairProInfoName orderPairNameIsOverlength">
                                                                <span class="isOverLengThHide">
                                                                    Warehouse：CN发货仓库</span>
                                                            </p>
                                                        </div>
                                            </td>
                                        </tr>
                                    </table>
                                <table class="cancel-order-table">
                                       </table>
                            </td>
                            <td class="minW100"
                                data-platform="pddkj"
                                data-realPlatform="popTemu"
                                data-shopName="溶溶-半托管"
                                data-smtAutoOnline="1">
                                CNY&nbsp;0<i class="iconfont icon_help_outline m-top2 v-top md-18 f-w400 pointer myjSquare" data-content="<span class='f13 gray-c'>平台暂时不支持返回订单金额</span>"></i>
                                <br/>
                                <div class="relative m-top5 gray-c hover-prompt hoverPrompt pointer uClick small order-profit-prompt"
                                         id="profit_138220103902329600_box" data-placement="top" data-html="true">
                                        <div class="inline-block">
                                            <span id="p_138220103902329600"></span>
                                            <span id="unit_138220103902329600"></span>
                                            <span class="f13" id="profit_138220103902329600">--</span>
                                        </div>
                                        <div class="m-left20 f12 pointer" id="profitTypeShowDiv_138220103902329600">
                                            <span id="pShowTypeVal_138220103902329600"></span>
                                        </div>
                                    </div>
                                </td>
                            <td>
                                <span class="mRight10 white-space">******</span>
                                <span>「美国」</span>
                            </td>
                            <td class="tableOrderId">
                                <input class="orderNumberSpan_138220103902329600" type="hidden" value="PO-211-19026059203193730">
                                <a href="javascript:" class="limingcentUrlpic inline-block orderNumberSpan" onclick="dxmOrderDetail('138220103902329600');">PO-211-19026059203193730</a>&nbsp;
                                <p>
                                    <span class="squareSpan bg-dark-blue delayTips" data-container="body" data-toggle="popover" data-placement="top" data-content="<div>包含Y2模式中国直邮预售产品，SKU：6795236809<div>申请单号后默认延迟15天（360h）提交平台发货，<span class='f-red'>若您点击了虚拟发货/发货则会立刻提交平台发货，若长时间没有物流轨迹容易造成虚假发货考核&nbsp;&nbsp;</span><a target='_blank'  href='https://help.dianxiaomi.com/pre/getContent.htm?id=3039'>查看订单处理帮助</a></div><div>">Y2</span>
                                        </p>
                                </td>
                            <td>
                                <div>下单：2025-08-19 09:36</div>
                                <div>付款：2025-08-19 09:36</div><input type="hidden" value="" data-orderState="paid" data-applyTrackNumStatus="">
                                <div class="fColor6" id="timeLeft_138220103902329600">
                                        <script>addTimer("timeLeft_138220103902329600", 936810,"shipping");</script>
                                    </div>
                                </td>
                            <td class="min-w85 w85" rowspan="1">
                                    已付款<br/>
                                    <p class="m-top10">
                                            </p>
                                    <p class="m-top10">
                                            </p>
                                    </td>
                            <td class="p-top3 f-center minW80" >
                                    <a href="javascript:" onclick="auditPackage('138220103902329600');">审核</a><br/><a href="javascript:" onclick="showComment1('138220103902329600');">备注</a><br/><a href="javascript:" onclick="dxmPackageDetail('138220103902329600');">详情</a></td>
                            </tr>
                    </tbody>
    </table>
</form>
<script type="text/javascript">
    publicHoverTextCanBeMovedIn('.blacklistTruggerHover');
    $('.dataTriggerHover').popover({
        trigger: 'hover'
    });
    $('.dataTriggerClick').popover({
        trigger: 'click'
    });
    //鼠标点击位置如果不是popver，popverhide;
    $(document).off('click.orderPopover', 'body').on('click.orderPopover', 'body', function (e) {
        if(!e) e = window.event;

        //获取事件点击元素
        var targ = e.target,
            //获取元素名称
            popover = $(targ).closest('.popover').length,
            $popover = $('.popover'),
            popoverFa = $(targ).hasClass('dataTriggerClick');

        if(popover !== 1 && !popoverFa && $popover.length){
            $('[aria-describedby]').popover('hide'); //触发气泡事件把气泡移除
            $popover.remove(); //可能出现匹配不上未被移除的气泡，所以如果有未移除的气泡再移除一次
        }
    });
    $('[data-toggle="tooltip"]').tooltip();
    $('.amazonlatestDeliveryDate[data-toggle="popover"]').popover();
    //公用悬停显示文案可移入
    publicHoverTextCanBeMovedIn('.hoverShowCommonBrandMsg');
    // 注册买家备注气泡
    publicHoverTextCanBeMovedIn('.buyerNotes', 'top', true);
    expand_click3();
    $(document).ready(function(){
        // console.log('')
        CUSTOM_MARK.initView(1);
        var objs = $('.errorMsgHide');
        $.each(objs,function(i,j){
            var html = $(j).html();
            var arry = '';
            if(html){
                arry = strFilterUrl(html);
            }
            arry = arry.replace(/&lt;/g, '<').replace(/&gt;/g, '>').replace(/alert/g, '提示').replace(/location.href/g, '跳转到：');
            arry = removeScriptContent(arry); // 移出script标签
            arry = removeStyleContent(arry); // 移出style标签
            $(j).closest('.errorMsgBox').find('span.errorMsgShow').append(arry);
        });

        //创建商品
        var create = function (obj, info) {
            var $create = $(obj[0]).find('input.create'),
                $pair = $(obj[0]).find('input.pair'),
                id = $pair.attr('data-id'),
                sku = $create.attr('displaysku'),
                pid = $create.attr('pid'),
                vid = $create.attr('vid'),
                dataObj = {};
            if (!vid) {
                vid = pid;
            }
            dataObj.id = id;
            dataObj.sku = sku;
            dataObj.pid = pid;
            dataObj.vid = vid;

           comOpenWinPost('dxmCommodityProduct/viewCommodityProductFromOrder.htm', {orderInfo: JSON.stringify(dataObj)});
        };
        //配对
        var pair = function(obj,info){
            var $pair = $(obj[0]).find("input.pair");
            showDialogWareHoseProductList($pair.attr('data-id'),$pair.attr('data-index'),
                $pair.attr("data-state"),$pair.attr("data-overseawarehose"),$pair.attr("data-authid"),obj[0]);
        };
        //添加来源
        var addSource = function (obj) {
            var $pair = $(obj[0]).find('input.pair');
            getSourceUrl($pair.attr('data-id'),$pair.attr('data-index'));
        };
        //寻找货源
        var findLink = function(obj,info) {
            var imgUrl = info.imgUrl;
            imgUrl = imgUrl.replace('-tiny.jpg','-original.jpg').replace('_50x50.jpg','').replace('_80x80q80.jpg','').replace('.80x80','').replace('_100x100q100.jpg','_800x800q100.jpg');
            imgUrl = imgUrl.replace('$_6.', '$_19.').replace('.cloudfront.net/image/150_150', '.cloudfront.net/image/400_400');
            PURCHASE_SOURCE.getAlibabaListMethod(imgUrl);
        };
        //淘宝找货源
        var taoBaoFindLink = function(obj, info) {
            var platform = $(obj).find('.create').attr('platform'),
                url = $(obj).closest('tr').find('a.productUrl').attr('href');
            if (!platform && url && url.indexOf('aliexpress.com') !== -1)  platform = 'smt';

            // url = url && (platform === 'wish' || platform === 'ebay' || platform === 'amazon') ? 'productUrl='+url : 'imgUrl='+info.imgUrl;
            var imgUrl = info.imgUrl;
            imgUrl = imgUrl.replace('-tiny.jpg','-original.jpg').replace('_50x50.jpg','').replace('_80x80q80.jpg','').replace('.80x80','').replace('_100x100q100.jpg','_800x800q100.jpg');
            imgUrl = imgUrl.replace('$_6.', '$_19.').replace('.cloudfront.net/image/150_150', '.cloudfront.net/image/400_400');
            ORDER_SOURCE_FN.sourceDetail(imgUrl);
        };
        // 支持刊登的平台，用来判断图片是否显示添加来源
        var mayPublishPlatform = ['wish', 'smt', 'ebay', 'amazon', 'lazada', 'alibaba', 'fanno', 'shopify', 'dh', 'shopee', 'joom', 'tophatter', 'shoplazza', 'tiktok'];
        //注册图片放大
        // console.log('//注册图片放大')
        $('div.listImgIn').each(function(i, j) {
            $(j).off('mouseenter').on('mouseenter',function(event) {
            var $listImgIn = $(this);
            var stockProductNumvar = $('#stockProductNum').val();
            var creates = {
                name:'<i class="glyphicon glyphicon-edit"></i>创建',//必传
                type:'left', //必传
                call: create
            };
            var pairs =  {
                name:'<i class="glyphicon glyphicon-random"></i>配对',
                type:'left',
                call: pair
            };
            var addSources =  {
                name: '添加来源',
                type: 'left',
                call: addSource
            };
            var proId = $listImgIn.find('.create').attr('proid');
            if(proId){
                pairs.name = '<i class="glyphicon glyphicon-random"></i>更换';
            }
            var source1 = {//1688
                name:'<i class="glyphicon glyphicon-search"></i>找货源 1688',
                type:'right',
                call: findLink
            },
            source2 = {//淘宝
                name:'淘宝',
                type:'right',
                call: taoBaoFindLink
            };
            var type = $.trim($listImgIn.attr('data-type')),
                isCreate = $.trim($listImgIn.attr('data-hasProductId')),
                action = [],
                platform = $listImgIn.closest('tr[data-platform]').attr('data-platform');


            if (isCreate == "0" && stockProductNumvar == 1) {
                action.push(creates);
            }
            if(type == "1" || type == "0" && stockProductNumvar == 1) {
                action.push(pairs);
            }
            // 如果有来源链接权限
            if($('#orderSourceUrlType').val() === '1'){
                // 属于支持刊登的平台并且有配对才显示添加来源
                if(mayPublishPlatform.indexOf(platform) !== -1 && proId && (type === '1' || type === '0' && (+stockProductNumvar) === 1)){
                    action.push(addSources);
                }
            }
            action.push(source1);
            action.push(source2);
            $(this).extensiblePicZoom({
                type: 'order',
                divWidth: "350",
                divHeight:"350",
                action: action,
                initToShow: true,
                isHasTaoBao: true//是否加淘宝入口
            });
            $listImgIn = null;
            })
        });
        
            /*新版注册懒加载*/
            $('.imgCss.lazy').newLazyload({
                placeholder: "//www.dianxiaomi.com/static/img/loading5.gif",
                threshold: 1000,
                effect: "fadeIn",
                failurelimit: 20,
                skip_invisible: false
            });
        MYJ_PAGINATION.init(MYJ_PAGINATION.get([30, 50, 100, 300]), '', function(option) {
            if(+option.pageSize !== +$('#pageSize').val()) {
                typeof ORDER_STORAGE_SET !== 'undefined' && typeof ORDER_STORAGE_SET.set === 'function' && ORDER_STORAGE_SET.set('pageSize', option.pageSize)
            }
            pageReload(option);
            $('.normalBtnGroup').removeClass('myj-hide');
            $('.printBtnGroup').addClass('myj-hide');
        });
        MYJ_PAGINATION.init(MYJ_PAGINATION.get([30, 50, 100, 300], true), '#ceilingPage', function(option) {
            if(+option.pageSize !== +$('#pageSize').val()) {
                typeof ORDER_STORAGE_SET !== 'undefined' && typeof ORDER_STORAGE_SET.set === 'function' && ORDER_STORAGE_SET.set('pageSize', option.pageSize)
            }
            pageReload(option);
            $('.normalBtnGroup').removeClass('myj-hide');
            $('.printBtnGroup').addClass('myj-hide');
        });

        var $orderPairNameIsOverlength = $('.orderPairNameIsOverlength'),
            orderPairNameIsOverlengthHtml = '<div class="word-box order-word-box"><div class="whiteBar"></div>' +
                '<div class="word-in wordbox scroll-bar"></div>' +
                '<div class="triangle"></div></div>';
        $.each($orderPairNameIsOverlength, function (i, j) { //循环订单的商品信息
            var $obj = $(j);
            if($obj.height() > 80){ //判断当前信息节点高度是否超过80，如果超过了则表示当前内容超过4行显示了
                $obj.addClass('hover-prompt relative').find('.isOverLengThHide').addClass('no-new-line4'); //加超出4行隐藏class
                $obj.off('mouseover').on('mouseover',function () { //给当前节点加悬停气泡
                    var yanModalObj = $(orderPairNameIsOverlengthHtml),
                        newStr = $(this).find('.isOverLengThHide').html();
                    if ($(this).find('.word-box').length < 1) {
                        yanModalObj.find('.wordbox').append(newStr);
                        $(this).append(yanModalObj);
                    }
                });
            }
        });

        //不再关注按钮显示隐藏
        hasPutHoldMethod();
    });
</script>
</div>

<!-- 分页 -->
<div class="col-xs-12 p0" style="text-align:right;">
        <ul id="downPage" class="pageDiv" style="margin:2px 0px 2px 0;"></ul>
    </div>
<script type="text/javascript">
	$(document).ready(function(){
        var orderIdsStr='138220103904402488;138220103903910862;138220103903911010;138220103903911148;138220103903911256;138220103902559598;138220103902329518;138220103902329600';
        //获取当前页所有订单的id
        if(orderIdsStr !== ""){
            var orderIds= [],
                $keepState = $('#keepState');
            if(orderIdsStr.indexOf(";")){
                orderIds = orderIdsStr.split(";");
                $keepState.data("orderIds", orderIds);
                $keepState.data("currOrderNum",-1);//当前订单索引
            }
        }
        // 悬浮弹窗的渲染
        publicHoverTextCanBeMovedIn('.delayTips', 'top', false, 350, 200);

    });

    // 翻页请求
    function pageReload(screenObj){
        var url = "package/list.htm?pageNo="+screenObj.pageNo+"&pageSize="+screenObj.pageSize+"&shopId=6839796&state=paid&authId=-1&country=&platform=&isSearch=1" +
            "&startTime=&endTime=&orderField=order_pay_time&isVoided=0&isRemoved=0&ruleId=-1&sysRule=&applyType=" +
            "&applyStatus=&printJh=-1&printMd=-1&commitPlatform=&productStatus=&jhComment=-1&storageId=0" +
            "&history=&custom=-1&isOversea=-1&timeOut=0&refundStatus=0&forbiddenStatus=-1&forbiddenReason=0&behindTrack=-1",
            $shopGroupId = $('#shopGroupId'),
            shopGroupId = $shopGroupId.length ? $.trim($shopGroupId.val()) : '';

        if (shopGroupId) url += '&shopGroupId=' + shopGroupId;//如果选中了店铺分组，则加上店铺分组参数作为筛选条件
        MYJ.ajax({
            type: 'GET',
            url: url,
            data: {},
            success: function(data){
                try {
                    clearPageListMemory();
                } catch (e) {
                    console.log('src/main/webapp/WEB-INF/jsp/order/dxmPackageAjax.jsp')
                }
                $('#dxmBody').html(data);
                $('#selectedData').html('');
                refreshApprovedCountNum();
                refreshOrderProfit(0, 'packageId');
                refreshWarehouseInfo('packageId');
            }
        });
        $('#keepState').data({
            'ajaxurl': url,
            'ajaxpostdata': null,
            'pagesize': screenObj.pageSize
        });
    }
  	// 当前显示的是哪个状态(未处理、异常订单、待发货等)
 	var seledStateName = $("#keepState").data("stateName"),
       orderApplyStatus=$('#keepState').data('applystatus');
  	$("#currState").html(seledStateName);

  	// 显示 一键填充COD
    var isAllOnCod = $('#isAllOnCod').val();
    if (isAllOnCod === 'true' || isAllOnCod === true) {
        $('.butchUpdateCOD').removeClass('hide');
    } else {
        $('.butchUpdateCOD').addClass('hide');
    }
    // 小秘云仓-失败的才展示cod
    if (orderApplyStatus !== 'fail') {
        $('.batchCodFail').addClass('hide');
    }

    $(document).off('click','.operateBox .selectionRange ul li');
    $(document).on('click','.operateBox .selectionRange ul li',function(){
        var that = $(this),
            selectVal = that.attr('data-value');
        if(selectVal){
            that.closest('.operateBox').find('.operationResult .resultNumRange').html(selectVal).click();
        }
    });
    // 灰度显隐
    GRAY_AUTH_ALL && GRAY_AUTH_ALL.initDisplay();
</script>
