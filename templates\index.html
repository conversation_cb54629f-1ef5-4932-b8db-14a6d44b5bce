<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品数据提取工具</title>
    <style>
        :root {
            --primary-color: #4299E1;
            --success-color: #48BB78;
            --warning-color: #ED8936;
            --light-gray: #F5F7FA;
            --medium-gray: #E2E8F0;
            --dark-gray: #2D3748;
            --text-color: #4A5568;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei UI', 'Microsoft YaHei', 'Source Han Sans CN', 'Noto Sans CJK SC', 'WenQuanYi Micro Hei', sans-serif;
            background-color: var(--light-gray);
            color: var(--dark-gray);
            line-height: 1.6;
            height: 100vh;
            overflow: hidden;
        }

        .main-layout {
            display: grid;
            grid-template-columns: 400px 1fr 350px;
            grid-template-rows: 1fr;
            height: 100vh;
            gap: 0;
        }

        .left-panel {
            background: white;
            border-right: 1px solid var(--medium-gray);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .center-panel {
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .right-panel {
            background: white;
            border-left: 1px solid var(--medium-gray);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .panel-section {
            padding: 20px;
            border-bottom: 1px solid var(--medium-gray);
        }

        .panel-section:last-child {
            border-bottom: none;
            flex: 1;
            overflow: hidden;
        }

        .scrollable-content {
            overflow-y: auto;
            flex: 1;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            display: flex;
            flex-direction: column;
        }

        .card-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            color: var(--dark-gray);
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 5px;
            flex-shrink: 0;
        }

        .section-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 15px;
            color: var(--dark-gray);
            border-bottom: 1px solid var(--medium-gray);
            padding-bottom: 8px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }

        .form-group input[type="text"] {
            width: 100%;
            max-width: 300px;
            padding: 8px 12px;
            border: 1px solid var(--medium-gray);
            border-radius: 4px;
            font-size: 14px;
        }

        .form-group input[type="text"]:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
        }

        textarea {
            width: 100%;
            height: 200px;
            padding: 15px;
            border: 1px solid var(--medium-gray);
            border-radius: 4px;
            font-size: 14px;
            resize: vertical;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .btn-primary { background-color: var(--primary-color); color: white; }
        .btn-success { background-color: var(--success-color); color: white; }
        .btn-warning { background-color: var(--warning-color); color: white; }

        .button-group {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .button-group .btn {
            width: 100%;
            margin: 0;
        }

        .btn-nav {
            padding: 8px 16px;
            border: 1px solid #D1D5DB;
            border-radius: 4px;
            background-color: white;
            color: #374151;
            cursor: pointer;
        }

        .console {
            background-color: #2D3748;
            color: #F7FAFC;
            border-radius: 4px;
            height: 300px;
            overflow-y: auto;
            padding: 15px;
            font-family: 'Consolas', monospace;
            font-size: 13px;
        }
        .log-time { color: #A0AEC0; margin-right: 10px; }
        .log-success { color: #9AE6B4; }
        .log-warning { color: #F6E05E; }
        .log-error { color: #FEB2B2; }



        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
            gap: 12px;
            padding: 8px 0;
            align-items: start;
            grid-auto-rows: min-content;
        }

        .product-card {
            background: white;
            border-radius: 8px;
            padding: 12px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            border-left: 4px solid var(--primary-color);
            display: flex;
            flex-direction: column;
            gap: 8px;
            height: fit-content;
            min-height: 120px;
            align-self: start;
        }
        .product-card.completed {
            border-left-color: var(--success-color);
            background-color: #F0FFF4;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.8);
        }

        .modal-content {
            background-color: white;
            margin: 2% auto;
            border-radius: 8px;
            width: 95%;
            max-width: 1800px;
            height: 95vh;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            border-bottom: 1px solid var(--medium-gray);
            background-color: #F8F9FA;
        }
        .modal-header h3 { font-size: 18px; }

        .close {
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            color: #A0AEC0;
            background: none;
            border: none;
        }

        .modal-body {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            padding: 15px;
        }

        .comparison-area {
            flex: 1;
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            min-height: 0;
        }

        .comparison-pane-container {
            width: 50%;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .comparison-pane-header {
            text-align: center;
            padding: 8px;
            background-color: #F7FAFC;
            border-radius: 8px 8px 0 0;
            font-weight: bold;
            color: #2D3748;
            border: 1px solid var(--medium-gray);
            border-bottom: none;
            font-size: 14px;
        }

        .comparison-pane {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #F9FAFB;
            border-radius: 0 0 8px 8px;
            border: 1px solid var(--medium-gray);
            position: relative;
            overflow: hidden;
        }

        .comparison-pane img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            transition: transform 0.3s ease-out;
            transform: scale(1);
        }
        
        /* API图片默认放大25% */
        #apiPane img {
            transform: scale(1.25);
        }
        
        #apiPane:hover img {
            transform: scale(1.25);
        }
        
        #apiPane.zoomed img {
            transform: scale(3.125); /* 悬停时在1.25基础上再放大2.5倍 */
        }
        .comparison-pane:hover img {
            cursor: zoom-in;
        }
        .comparison-pane.zoomed img {
            transform: scale(2.5);
            cursor: crosshair;
        }
        .comparison-pane.zoomed .zoom-icon {
            display: none;
        }

        .local-thumbnails-area {
            height: 140px;
            border-top: 1px solid var(--medium-gray);
            padding-top: 15px;
        }

        .local-thumbnails-grid {
            height: 100%;
            display: flex;
            gap: 10px;
            overflow-x: auto;
            padding-bottom: 10px;
        }

        .thumbnail-item {
            height: 120px;
            width: 120px;
            flex-shrink: 0;
            border: 3px solid transparent;
            border-radius: 6px;
            cursor: pointer;
            overflow: hidden;
            position: relative;
            background-color: var(--medium-gray);
        }
        .thumbnail-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .thumbnail-item.selected {
            border-color: var(--success-color);
            box-shadow: 0 0 10px rgba(72, 187, 120, 0.5);
        }
        .thumbnail-item:hover {
            border-color: var(--primary-color);
        }

        .modal-footer {
            padding: 15px 20px;
            border-top: 1px solid var(--medium-gray);
            background-color: #F8F9FA;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
        }

        .image-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #9CA3AF;
        }
        .image-placeholder-icon { font-size: 48px; margin-bottom: 10px; }
        
        .hidden { display: none; }

        .image-viewer-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.85);
            z-index: 9999;
            justify-content: center;
            align-items: center;
            cursor: zoom-out;
        }
        .image-viewer-overlay img {
            max-width: 90vw;
            max-height: 90vh;
            box-shadow: 0 0 30px rgba(0,0,0,0.5);
        }
        .zoom-icon {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 32px;
            height: 32px;
            background-color: rgba(0,0,0,0.5);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            cursor: zoom-in;
            opacity: 0;
            transition: opacity 0.2s;
            z-index: 10;
        }
        .comparison-pane:hover .zoom-icon {
            opacity: 1;
        }

        /* 通知系统样式 */
        .notification-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            pointer-events: none;
        }

        .notification {
            background: white;
            border-radius: 8px;
            padding: 16px 20px;
            margin-bottom: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            border-left: 4px solid var(--primary-color);
            min-width: 300px;
            max-width: 400px;
            pointer-events: auto;
            transform: translateX(100%);
            opacity: 0;
            transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        .notification.show {
            transform: translateX(0);
            opacity: 1;
        }

        .notification.success {
            border-left-color: var(--success-color);
        }

        .notification.warning {
            border-left-color: var(--warning-color);
        }

        .notification.error {
            border-left-color: #E53E3E;
        }

        .notification-content {
            display: flex;
            align-items: flex-start;
            gap: 12px;
        }

        .notification-icon {
            font-size: 20px;
            flex-shrink: 0;
            margin-top: 2px;
        }

        .notification-text {
            flex: 1;
            font-size: 14px;
            line-height: 1.4;
        }

        .notification-close {
            background: none;
            border: none;
            font-size: 18px;
            color: #A0AEC0;
            cursor: pointer;
            padding: 0;
            margin-left: 8px;
            flex-shrink: 0;
        }

        .notification-close:hover {
            color: var(--dark-gray);
        }
    </style>
</head>
<body>
    <!-- 通知容器 -->
    <div id="notificationContainer" class="notification-container"></div>

    <div class="main-layout">
        <!-- 左侧面板 -->
        <div class="left-panel">
            <!-- 配置选项 -->
            <div class="panel-section">
                <div class="section-title">配置选项</div>
                <div class="form-group">
                    <label>搜索路径：</label>
                    <input type="text" id="searchPath" value="E:\图片\原图">
                </div>
                <div class="form-group">
                    <label>目标路径后缀：</label>
                    <input type="text" id="pathFilter" value="\导出图\已完成">
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" id="enableTargetSuffix" checked>
                    <label for="enableTargetSuffix">启用目标路径后缀检测</label>
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" id="strictSearch" checked>
                    <label for="strictSearch">严格搜索路径限制</label>
                </div>
            </div>

            <!-- SKU搜索 -->
            <div class="panel-section">
                <div class="section-title">SKU搜索</div>
                <div class="form-group">
                    <input type="text" id="skuInput" placeholder="输入SKU进行搜索..." style="width: 100%; margin-bottom: 10px;">
                    <button class="btn btn-primary" onclick="searchBySKU()" style="width: 100%;">搜索商品</button>
                </div>
            </div>

            <!-- API数据获取 -->
            <div class="panel-section">
                <div class="section-title">API数据获取</div>
                <div class="button-group">
                    <button class="btn btn-primary" onclick="fetchDataFromAPI()">从API获取数据</button>
                    <button class="btn btn-warning" onclick="showConfigModal()">配置管理</button>
                    <button class="btn btn-warning" onclick="startImageSelection()">开始选择图片</button>
                </div>
            </div>




        </div>

        <!-- 中间面板 -->
        <div class="center-panel">
            <div class="card" style="margin: 20px; height: calc(100vh - 40px); overflow: hidden;">
                <!-- SKU搜索结果区域 -->
                <div id="skuResultSection" class="hidden" style="height: 100%; display: flex; flex-direction: column;">
                    <div class="card-title">SKU搜索结果</div>
                    <div id="skuResultContent" style="flex: 1; overflow-y: auto; padding: 20px;">
                        <!-- SKU搜索结果将在这里显示 -->
                    </div>
                </div>

                <!-- 商品列表区域 -->
                <div id="productsSection" class="hidden" style="height: 100%; display: flex; flex-direction: column;">
                    <div class="card-title">商品列表</div>
                    <div id="productsGrid" class="products-grid" style="flex: 1; overflow-y: auto; max-height: calc(100vh - 120px);"></div>
                </div>

                <!-- 欢迎界面 -->
                <div id="welcomeSection" style="display: flex; align-items: center; justify-content: center; height: 100%; color: #9CA3AF; text-align: center;">
                    <div>
                        <div style="font-size: 48px; margin-bottom: 20px;">📦</div>
                        <div style="font-size: 18px; margin-bottom: 10px;">商品数据提取工具</div>
                        <div style="font-size: 14px; margin-bottom: 8px;">• 输入SKU搜索单个商品信息</div>
                        <div style="font-size: 14px;">• 点击"从API获取数据"批量处理订单</div>
                        <div style="font-size: 12px; margin-top: 8px; color: #A0AEC0;">支持SKU搜索和批量数据处理</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧面板 - 操作日志 -->
        <div class="right-panel">
            <div class="panel-section scrollable-content" style="height: 100%;">
                <div class="section-title">操作日志</div>
                <div id="console" class="console" style="height: calc(100% - 50px); margin: 0;"></div>
            </div>
        </div>


    </div>

    <!-- Status Bar -->
    <div class="status-bar"><span id="statusText">就绪</span></div>

    <!-- Image Viewer Overlay -->
    <div id="imageViewer" class="image-viewer-overlay" onclick="hideViewer()"><img id="viewerImg" src=""></div>

    <!-- Image Selection Modal -->
    <div id="imageModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">图片对比选择</h3>
                <button class="close" onclick="closeImageModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="comparison-area">
                    <div class="comparison-pane-container">
                        <div class="comparison-pane-header">🛍️ 商品图</div>
                        <div id="apiPane" class="comparison-pane"></div>
                    </div>
                    <div class="comparison-pane-container">
                        <div class="comparison-pane-header">🖼️ 对比图片</div>
                        <div id="localPane" class="comparison-pane"></div>
                    </div>
                </div>
                <div class="local-thumbnails-area">
                    <div id="localThumbnailsGrid" class="local-thumbnails-grid"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-nav" id="prevBtn" onclick="navigateProduct(-1)">上一个</button>
                <button class="btn-nav" id="nextBtn" onclick="navigateProduct(1)">下一个</button>
                <button class="btn btn-success" onclick="confirmSelection()">确认选择</button>
                <button class="btn" onclick="skipProduct()" style="background-color: #A0AEC0; color: white;">跳过</button>
                <button class="btn" onclick="closeImageModal()" style="background-color: #6B7280; color: white;">关闭</button>
            </div>
        </div>
    </div>

    <!-- Config Modal -->
    <div id="configModal" class="modal">
        <div class="modal-content" style="height: 80vh; max-width: 800px;">
            <div class="modal-header">
                <h3>API配置管理</h3>
                <button class="close" onclick="closeConfigModal()">&times;</button>
            </div>
            <div class="modal-body" style="padding: 20px;">
                <div class="form-group">
                    <label>API地址：</label>
                    <textarea id="configApiUrl" style="height: 80px; width: 100%; resize: vertical;" placeholder="输入API地址..."></textarea>
                </div>
                <div class="form-group">
                    <label>Cookie：</label>
                    <textarea id="configCookie" style="height: 150px; width: 100%; resize: vertical;" placeholder="输入Cookie内容..."></textarea>
                </div>
                <div class="form-group">
                    <label>基础URL：</label>
                    <input type="text" id="configBaseUrl" style="width: 100%;" placeholder="https://www.dianxiaomi.com">
                </div>
                <div class="form-group">
                    <label>SKU搜索URL：</label>
                    <input type="text" id="configSkuSearchUrl" style="width: 100%;" placeholder="https://www.dianxiaomi.com/api/popTemuProduct/pageList.json">
                </div>
                <div class="form-group">
                    <label>Referer：</label>
                    <input type="text" id="configReferer" style="width: 100%;" placeholder="https://www.dianxiaomi.com/">
                </div>
                <div class="form-group">
                    <label>共享文件夹路径：</label>
                    <input type="text" id="configSharedFolder" style="width: 100%;" placeholder="\\192.168.1.200\hhr-图库\合伙人-半托出单图\亚克力摆件\丽生-亚克力摆件">
                    <small style="color: #666; font-size: 12px;">用于SKU筛选的共享文件夹路径，只处理该文件夹中不存在的SKU</small>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" onclick="loadConfig()">加载配置</button>
                <button class="btn btn-success" onclick="saveConfig()">保存配置</button>
                <button class="btn" onclick="closeConfigModal()" style="background-color: #6B7280; color: white;">关闭</button>
            </div>
        </div>
    </div>

    <script>
        // State variables
        let currentProducts = [];
        let currentJsonData = {};
        let selectedImages = {};
        let currentProductIndex = 0;
        let currentLocalImages = [];
        let currentApiImageUrl = '';
        let currentApiImageUrlRaw = '';
        let selectedImageInfo = null;
        let eventSource = null;
        let isProcessing = false;

        // DOM Elements
        const $ = (selector) => document.querySelector(selector);
        const $$ = (selector) => document.querySelectorAll(selector);

        // Viewer Functions
        function showViewer(src) {
            event.stopPropagation();
            $('#viewerImg').src = src;
            $('#imageViewer').style.display = 'flex';
        }
        function hideViewer() {
            $('#imageViewer').style.display = 'none';
        }

        // 通知系统
        function showNotification(message, type = 'info', duration = 4000) {
            const container = $('#notificationContainer');
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;

            const icons = {
                success: '✅',
                warning: '⚠️',
                error: '❌',
                info: 'ℹ️'
            };

            notification.innerHTML = `
                <div class="notification-content">
                    <div class="notification-icon">${icons[type] || icons.info}</div>
                    <div class="notification-text">${message}</div>
                    <button class="notification-close" onclick="removeNotification(this.parentElement.parentElement)">&times;</button>
                </div>
            `;

            container.appendChild(notification);

            // 触发动画
            setTimeout(() => notification.classList.add('show'), 10);

            // 自动移除
            if (duration > 0) {
                setTimeout(() => removeNotification(notification), duration);
            }

            return notification;
        }

        function removeNotification(notification) {
            if (!notification || !notification.parentElement) return;
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.parentElement.removeChild(notification);
                }
            }, 300);
        }

        // 实时日志连接管理
        function connectToLogStream() {
            if (eventSource) {
                eventSource.close();
            }

            eventSource = new EventSource('/api/logs/stream');

            eventSource.onopen = function() {
                console.log('日志流连接已建立');
            };

            eventSource.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);

                    if (data.type === 'connected') {
                        console.log('SSE连接成功，会话ID:', data.session_id);
                    } else if (data.type === 'heartbeat') {
                        // 心跳包，不需要处理
                    } else if (data.type === 'completion') {
                        // 处理完成信号
                        handleProcessingCompletion(data);
                    } else {
                        // 普通日志消息
                        addLogRealtime(data.message, data.type);
                    }
                } catch (e) {
                    console.error('解析日志数据失败:', e);
                }
            };

            eventSource.onerror = function(event) {
                console.error('日志流连接错误:', event);
                if (eventSource.readyState === EventSource.CLOSED) {
                    console.log('日志流连接已关闭，尝试重连...');
                    setTimeout(connectToLogStream, 3000);
                }
            };
        }

        function handleProcessingCompletion(data) {
            isProcessing = false;

            if (data.success) {
                currentProducts = data.products || [];
                displayProducts();
                showNotification(data.message, 'success');
                addLogRealtime('🎉 数据处理完成！', 'success');
            } else {
                showNotification(data.message, 'error');
                addLogRealtime('❌ 数据处理失败', 'error');
            }

            // 重新启用按钮
            const fetchBtn = document.querySelector('button[onclick="fetchDataFromAPI()"]');
            if (fetchBtn) {
                fetchBtn.disabled = false;
                fetchBtn.textContent = '从API获取数据';
            }
        }

        // UI Update Functions
        function addLog(message, type = 'info') {
            const consoleEl = $('#console');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.innerHTML = `<span class="log-time">[${new Date().toLocaleTimeString()}]</span> ${message}`;
            consoleEl.appendChild(logEntry);
            consoleEl.scrollTop = consoleEl.scrollHeight;
        }

        function addLogRealtime(message, type = 'info') {
            const consoleEl = $('#console');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            const timestamp = new Date().toLocaleTimeString();
            logEntry.innerHTML = `<span class="log-time">[${timestamp}]</span> ${message}`;
            consoleEl.appendChild(logEntry);
            consoleEl.scrollTop = consoleEl.scrollHeight;
        }

        function clearLog() { $('#console').innerHTML = ''; }



        // Core App Logic

        // SKU搜索功能
        async function searchBySKU() {
            const skuInput = $('#skuInput');
            const sku = skuInput.value.trim();

            if (!sku) {
                showNotification('请输入SKU', 'warning');
                skuInput.focus();
                return;
            }

            // 禁用搜索按钮
            const searchBtn = document.querySelector('button[onclick="searchBySKU()"]');
            const originalText = searchBtn.textContent;
            searchBtn.disabled = true;
            searchBtn.textContent = '搜索中...';

            addLogRealtime(`🔍 正在搜索SKU: ${sku}`, 'info');

            try {
                const response = await fetch('/api/search_sku', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ sku: sku })
                });

                const result = await response.json();

                if (result.success) {
                    displaySKUResult(result);
                    addLogRealtime(`✅ SKU搜索成功: ${result.product_name}`, 'success');
                    showNotification(`找到商品: ${result.product_name}`, 'success');
                } else {
                    addLogRealtime(`❌ SKU搜索失败: ${result.message}`, 'error');
                    showNotification(result.message, 'error');
                    showWelcomeSection();
                }

            } catch (error) {
                addLogRealtime(`❌ SKU搜索异常: ${error.message}`, 'error');
                showNotification(`搜索失败: ${error.message}`, 'error');
                showWelcomeSection();
            } finally {
                // 恢复搜索按钮
                searchBtn.disabled = false;
                searchBtn.textContent = originalText;
            }
        }

        // 显示SKU搜索结果
        function displaySKUResult(result) {
            const content = $('#skuResultContent');

            // 构建商品图片HTML
            let imageHtml = '';
            if (result.image_url) {
                imageHtml = `
                    <div style="text-align: center; margin-bottom: 20px;">
                        <img src="${result.image_url}"
                             style="max-width: 100%; max-height: 300px; object-fit: contain; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.1); cursor: pointer;"
                             onclick="showViewer('${result.image_url}')"
                             onerror="this.style.display='none'; this.nextElementSibling.style.display='block';"
                             title="点击查看大图" />
                        <div style="display: none; color: #9CA3AF; font-size: 14px; margin-top: 10px;">📷 图片加载失败</div>
                    </div>
                `;
            } else {
                imageHtml = `
                    <div style="text-align: center; margin-bottom: 20px; padding: 40px; background-color: #F7FAFC; border-radius: 8px; border: 2px dashed #E2E8F0;">
                        <div style="font-size: 48px; color: #A0AEC0; margin-bottom: 10px;">📷</div>
                        <div style="color: #9CA3AF; font-size: 14px;">暂无商品图片</div>
                    </div>
                `;
            }

            content.innerHTML = `
                <div style="background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                    ${imageHtml}

                    <div style="margin-bottom: 15px;">
                        <label style="font-weight: bold; color: var(--dark-gray); display: block; margin-bottom: 5px;">SKU:</label>
                        <div style="padding: 8px 12px; background-color: #F7FAFC; border-radius: 4px; font-family: monospace; cursor: pointer; transition: background-color 0.2s;"
                             onclick="copyToClipboard('${result.sku}', 'SKU')"
                             onmouseover="this.style.backgroundColor='#E2E8F0'"
                             onmouseout="this.style.backgroundColor='#F7FAFC'"
                             title="点击复制SKU">${result.sku}</div>
                    </div>

                    <div style="margin-bottom: 15px;">
                        <label style="font-weight: bold; color: var(--dark-gray); display: block; margin-bottom: 5px;">商品名称:</label>
                        <div style="padding: 8px 12px; background-color: #F7FAFC; border-radius: 4px; line-height: 1.5; cursor: pointer; transition: background-color 0.2s;"
                             onclick="copyToClipboard('${result.product_name.replace(/'/g, "\\'")}', '商品名称')"
                             onmouseover="this.style.backgroundColor='#E2E8F0'"
                             onmouseout="this.style.backgroundColor='#F7FAFC'"
                             title="点击复制商品名称">${result.product_name}</div>
                    </div>

                    ${result.image_path ? `
                    <div style="margin-bottom: 15px;">
                        <label style="font-weight: bold; color: var(--dark-gray); display: block; margin-bottom: 5px;">图片路径:</label>
                        <div style="padding: 8px 12px; background-color: #F7FAFC; border-radius: 4px; font-size: 12px; color: #666; word-break: break-all; cursor: pointer; transition: background-color 0.2s;"
                             onclick="copyToClipboard('${result.image_path.replace(/\\/g, '\\\\')}', '图片路径')"
                             onmouseover="this.style.backgroundColor='#E2E8F0'"
                             onmouseout="this.style.backgroundColor='#F7FAFC'"
                             title="点击复制图片路径">${result.image_path}</div>
                    </div>
                    ` : ''}

                    <div style="text-align: center; margin-top: 20px;">
                        <button class="btn btn-primary" onclick="searchAnotherSKU()" style="margin-right: 10px;">搜索其他SKU</button>
                        <button class="btn btn-success" onclick="showWelcomeSection()">返回主页</button>
                    </div>
                </div>
            `;

            // 显示SKU结果区域，隐藏其他区域
            showSKUResultSection();
        }

        // 搜索其他SKU
        function searchAnotherSKU() {
            $('#skuInput').value = '';
            $('#skuInput').focus();
            showWelcomeSection();
        }

        // 显示不同的区域
        function showSKUResultSection() {
            $('#skuResultSection').classList.remove('hidden');
            $('#productsSection').classList.add('hidden');
            $('#welcomeSection').style.display = 'none';
        }

        function showProductsSection() {
            $('#skuResultSection').classList.add('hidden');
            $('#productsSection').classList.remove('hidden');
            $('#welcomeSection').style.display = 'none';
        }

        function showWelcomeSection() {
            $('#skuResultSection').classList.add('hidden');
            $('#productsSection').classList.add('hidden');
            $('#welcomeSection').style.display = 'flex';
        }

        // API数据获取功能
        async function fetchDataFromAPI() {
            if (isProcessing) {
                showNotification('数据处理正在进行中，请等待完成', 'warning');
                return;
            }

            clearLog();
            isProcessing = true;

            // 禁用按钮并显示处理状态
            const fetchBtn = document.querySelector('button[onclick="fetchDataFromAPI()"]');
            if (fetchBtn) {
                fetchBtn.disabled = true;
                fetchBtn.textContent = '处理中...';
            }

            addLogRealtime('🚀 开始从API获取数据...', 'info');
            showNotification('正在从API获取数据，请查看实时日志...', 'info');

            try {
                const response = await fetch('/api/fetch_data', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                const result = await response.json();

                if (!result.success) {
                    throw new Error(result.message);
                }

                if (result.async) {
                    addLogRealtime('✅ 数据处理已启动，正在后台处理...', 'info');
                    showNotification('数据处理已启动，请查看实时日志进度', 'success');
                } else {
                    // 兼容旧的同步处理方式
                    if (result.logs) {
                        result.logs.forEach(log => addLogRealtime(log.message, log.type));
                    }

                    currentProducts = result.products || [];
                    currentJsonData = {};
                    displayProducts();
                    addLogRealtime(result.message, 'success');
                    showNotification(`API数据获取成功！共获取 ${result.products.length} 条订单`, 'success');

                    isProcessing = false;
                    if (fetchBtn) {
                        fetchBtn.disabled = false;
                        fetchBtn.textContent = '从API获取数据';
                    }
                }

            } catch (error) {
                addLogRealtime(`❌ API数据获取失败: ${error.message}`, 'error');
                showNotification(`API数据获取失败: ${error.message}`, 'error');

                isProcessing = false;
                if (fetchBtn) {
                    fetchBtn.disabled = false;
                    fetchBtn.textContent = '从API获取数据';
                }
            }
        }

        // 配置管理功能
        function showConfigModal() {
            $('#configModal').style.display = 'block';
            loadConfig(); // 自动加载当前配置
        }

        function closeConfigModal() {
            $('#configModal').style.display = 'none';
        }

        async function loadConfig() {
            try {
                const response = await fetch('/api/get_config');
                const result = await response.json();
                
                if (result.success) {
                    const config = result.config;
                    $('#configApiUrl').value = config.api_url || '';
                    $('#configCookie').value = config.cookie || '';
                    $('#configBaseUrl').value = config.base_url || '';
                    $('#configSkuSearchUrl').value = config.sku_search_url || '';
                    $('#configReferer').value = config.referer || '';
                    $('#configSharedFolder').value = config.shared_folder || '';
                    
                    addLog('✅ 配置加载成功', 'success');
                    showNotification('配置加载成功', 'success');
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                addLog(`❌ 加载配置失败: ${error.message}`, 'error');
                showNotification(`加载配置失败: ${error.message}`, 'error');
            }
        }

        async function saveConfig() {
            try {
                const configData = {
                    api_url: $('#configApiUrl').value.trim(),
                    cookie: $('#configCookie').value.trim(),
                    base_url: $('#configBaseUrl').value.trim(),
                    sku_search_url: $('#configSkuSearchUrl').value.trim(),
                    referer: $('#configReferer').value.trim(),
                    shared_folder: $('#configSharedFolder').value.trim()
                };

                const response = await fetch('/api/update_config', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(configData)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    addLog('✅ 配置保存成功', 'success');
                    showNotification('配置保存成功', 'success');
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                addLog(`❌ 保存配置失败: ${error.message}`, 'error');
                showNotification(`保存配置失败: ${error.message}`, 'error');
            }
        }

        async function saveSearchConfig() {
            try {
                const configData = {
                    search_path: $('#searchPath').value.trim(),
                    target_suffix: $('#pathFilter').value.trim(),
                    enable_target_suffix: $('#enableTargetSuffix').checked,
                    strict_search: $('#strictSearch').checked
                };

                const response = await fetch('/api/update_search_config', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(configData)
                });

                const result = await response.json();

                if (result.success) {
                    addLog('✅ 搜索配置保存成功', 'success');
                    showNotification('搜索配置保存成功', 'success', 2000);
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                addLog(`❌ 保存搜索配置失败: ${error.message}`, 'error');
                showNotification(`保存搜索配置失败: ${error.message}`, 'error');
            }
        }

        async function loadSearchConfig() {
            try {
                const response = await fetch('/api/get_config');
                const result = await response.json();

                if (result.success) {
                    const config = result.config;

                    // 加载搜索配置到界面
                    if (config.search_base_path) {
                        $('#searchPath').value = config.search_base_path;
                    }
                    if (config.search_target_suffix) {
                        $('#pathFilter').value = config.search_target_suffix;
                    }
                    if (typeof config.search_enable_target_suffix !== 'undefined') {
                        $('#enableTargetSuffix').checked = config.search_enable_target_suffix;
                    }
                    if (typeof config.strict_search !== 'undefined') {
                        $('#strictSearch').checked = config.strict_search;
                    }

                    addLog('✅ 搜索配置加载成功', 'success');
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                addLog(`❌ 加载搜索配置失败: ${error.message}`, 'error');
            }
        }

        function setupConfigListeners() {
            // 搜索路径变更监听
            $('#searchPath').addEventListener('blur', saveSearchConfig);

            // 目标路径后缀变更监听
            $('#pathFilter').addEventListener('blur', saveSearchConfig);

            // 启用目标路径后缀检测变更监听
            $('#enableTargetSuffix').addEventListener('change', saveSearchConfig);

            // 严格搜索变更监听
            $('#strictSearch').addEventListener('change', saveSearchConfig);
        }

        // 复制到剪切板功能
        async function copyToClipboard(text, type) {
            try {
                await navigator.clipboard.writeText(text);
                showNotification(`${type}已复制到剪切板`, 'success', 2000);
                addLog(`✓ ${type}已复制: ${text}`, 'success');
            } catch (err) {
                // 如果现代API失败，尝试使用传统方法
                try {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    showNotification(`${type}已复制到剪切板`, 'success', 2000);
                    addLog(`✓ ${type}已复制: ${text}`, 'success');
                } catch (fallbackErr) {
                    showNotification(`复制失败: ${fallbackErr.message}`, 'error');
                    addLog(`× 复制失败: ${fallbackErr.message}`, 'error');
                }
            }
        }

        function displayProducts() {
            const grid = $('#productsGrid');
            grid.innerHTML = '';
            currentProducts.forEach((product, index) => {
                const isCompleted = selectedImages[product.id];
                const card = document.createElement('div');
                card.className = `product-card ${isCompleted ? 'completed' : ''}`;
                
                // 构建商品图片显示
                let productImageHtml = '';
                if (product.local_image_path || product.image_url) {
                    const imageUrl = product.local_image_path ? 
                        `/api/local_image?path=${encodeURIComponent(product.local_image_path)}` : 
                        (product.image_url ? `/api/image_proxy?url=${encodeURIComponent(product.image_url)}` : '');
                    
                    if (imageUrl) {
                        productImageHtml = `
                            <div style="margin-bottom: 8px; text-align: center;">
                                <img src="${imageUrl}" 
                                     style="max-width: 100%; max-height: 80px; object-fit: contain; border-radius: 4px; cursor: pointer;" 
                                     onclick="showViewer('${imageUrl}')"
                                     onerror="this.style.display='none'"
                                     title="点击查看大图" />
                            </div>
                        `;
                    }
                }
                
                card.innerHTML = `
                    ${productImageHtml}
                    <div style="font-size: 14px; line-height: 1.4; color: var(--dark-gray); cursor: pointer; padding: 2px 4px; border-radius: 3px; transition: background-color 0.2s;"
                         onclick="copyToClipboard('${product.name.replace(/'/g, "\\'")}', '商品名称')"
                         onmouseover="this.style.backgroundColor='#E2E8F0'"
                         onmouseout="this.style.backgroundColor='transparent'"
                         title="点击复制商品名称">${product.name}</div>
                    <div style="font-size: 12px; color: #9CA3AF; cursor: pointer; padding: 2px 4px; border-radius: 3px; transition: background-color 0.2s;"
                         onclick="copyToClipboard('${product.id}', 'SKU')"
                         onmouseover="this.style.backgroundColor='#E2E8F0'"
                         onmouseout="this.style.backgroundColor='transparent'"
                         title="点击复制SKU">SKU: ${product.id}</div>
                    <button class="btn btn-primary" style="font-size: 12px; padding: 8px 12px;" onclick="selectProductImage(${index})">
                        ${isCompleted ? '重新选择' : '选择图片'}
                    </button>
                `;
                grid.appendChild(card);
            });
            showProductsSection();
        }

        // Image Selection Modal Logic
        function startImageSelection() {
            if (currentProducts.length === 0) {
                showNotification('请先从API获取数据', 'warning');
                return;
            }
            showNotification('开始图片选择流程', 'info');
            selectProductImage(0);
        }

        async function selectProductImage(index) {
            currentProductIndex = index;
            const product = currentProducts[index];
            addLog(`🔍 正在为第 ${index + 1}/${currentProducts.length} 个商品搜索图片...`, 'info');
            try {
                const response = await fetch('/api/search_images', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        product_name: product.name,
                        product_id: product.id,
                        search_path: $('#searchPath').value,
                        strict_search: $('#strictSearch').checked,
                        enable_target_suffix: $('#enableTargetSuffix').checked,
                        json_data: currentJsonData
                    })
                });
                const result = await response.json();
                if (!result.success) throw new Error(result.message);

                currentLocalImages = result.local_images;
                currentApiImageUrl = result.api_image_url;
                currentApiImageUrlRaw = result.api_image_url_raw;
                addLog(`✓ 找到 ${result.local_images.length} 张本地图片`, 'success');
                showImageModal();
            } catch (error) {
                addLog(`× 搜索图片失败: ${error.message}`, 'error');
            }
        }

        function showImageModal() {
            $('#modalTitle').textContent = `图片对比 (${currentProductIndex + 1} / ${currentProducts.length})`;
            $('#prevBtn').disabled = currentProductIndex === 0;
            $('#nextBtn').disabled = currentProductIndex === currentProducts.length - 1;
            
            displayApiImage();
            displayLocalThumbnails();
            
            $('#imageModal').style.display = 'block';
            
            // 重新初始化缩放功能
            setTimeout(() => {
                reinitializeZoomForModal();
            }, 100);
        }

        function displayApiImage() {
            const pane = $('#apiPane');
            if (currentApiImageUrl) {
                const viewerUrl = currentApiImageUrlRaw || currentApiImageUrl;
                const isLocalImage = currentApiImageUrl.includes('/api/local_image');
                const imageTitle = isLocalImage ? '商品图（已下载）' : '商品图（在线）';
                pane.innerHTML = `
                    <span class="zoom-icon" onclick="showViewer('${viewerUrl}')">🔍</span>
                    <img src="${currentApiImageUrl}" alt="${imageTitle}" title="${imageTitle}" onerror="this.parentElement.innerHTML = createPlaceholder('📷', '商品图加载失败');">
                `;
            } else {
                pane.innerHTML = createPlaceholder('📷', '无商品图');
            }
        }

        function displayLocalThumbnails() {
            const grid = $('#localThumbnailsGrid');
            grid.innerHTML = '';
            selectedImageInfo = null;
            $('#localPane').innerHTML = createPlaceholder('🖼️', '请从下方选择本地图片');

            if (currentLocalImages.length > 0) {
                currentLocalImages.forEach((image, index) => {
                    const thumb = document.createElement('div');
                    thumb.className = 'thumbnail-item';
                    thumb.dataset.index = index;
                    thumb.innerHTML = `<img src="${image.url}" alt="${image.name}" onerror="this.parentElement.innerHTML = createPlaceholder('🖼️', '加载失败');">`;
                    thumb.onclick = () => updateLocalComparisonPane(image, thumb);
                    grid.appendChild(thumb);
                });
                // Auto-select the first one
                grid.firstChild.click();
            } else {
                grid.innerHTML = `<div style="width:100%; text-align:center; color:#9CA3AF;">未找到本地图片</div>`;
            }
        }

        function updateLocalComparisonPane(image, thumbElement) {
            $$('.thumbnail-item').forEach(t => t.classList.remove('selected'));
            thumbElement.classList.add('selected');

            const pane = $('#localPane');
            pane.innerHTML = `
                <span class="zoom-icon" onclick="showViewer('${image.url}')">🔍</span>
                <img src="${image.url}" alt="本地图" onerror="this.parentElement.innerHTML = createPlaceholder('🖼️', '本地图片加载失败');">
            `;
            
            selectedImageInfo = {
                type: 'local',
                url: image.url,
                extension: image.name.substring(image.name.lastIndexOf('.'))
            };
            
            // 重新初始化缩放功能
            setTimeout(() => {
                reinitializeZoomForModal();
            }, 100);
        }

        function createPlaceholder(icon, text) {
            return `<div class="image-placeholder"><div class="image-placeholder-icon">${icon}</div><div>${text}</div></div>`;
        }

        async function navigateProduct(direction) {
            const newIndex = currentProductIndex + direction;
            if (newIndex >= 0 && newIndex < currentProducts.length) {
                await selectProductImage(newIndex);
                // 重新初始化缩放功能
                setTimeout(() => {
                    reinitializeZoomForModal();
                }, 100);
            }
        }

        async function confirmSelection() {
            if (!selectedImageInfo) {
                showNotification('请先选择一张图片', 'warning');
                return;
            }
            const product = currentProducts[currentProductIndex];
            addLog(`⏳ 正在下载 ${product.id}...`, 'info');
            try {
                const response = await fetch('/api/download_selected', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        product_id: product.id,
                        ...selectedImageInfo
                    })
                });
                const result = await response.json();
                if (!result.success) throw new Error(result.message);

                selectedImages[product.id] = selectedImageInfo;
                addLog(`✓ 图片下载成功: ${result.file_path}`, 'success');
                showNotification(`图片下载成功: ${product.id}`, 'success');
                displayProducts();

                if (currentProductIndex < currentProducts.length - 1) {
                    await navigateProduct(1);
                } else {
                    addLog('🎉 所有商品图片选择完成！', 'success');
                    showNotification('所有商品图片选择完成！', 'success');
                    closeImageModal();
                }
            } catch (error) {
                addLog(`× 下载失败: ${error.message}`, 'error');
                showNotification(`下载失败: ${error.message}`, 'error');
            }
        }

        async function skipProduct() {
            addLog(`⏭️ 跳过商品 ${currentProducts[currentProductIndex].id}`, 'warning');
            if (currentProductIndex < currentProducts.length - 1) {
                await navigateProduct(1);
            } else {
                addLog('已是最后一个商品', 'info');
                closeImageModal();
            }
        }

        function closeImageModal() { $('#imageModal').style.display = 'none'; }

        // Global Event Listeners
        document.addEventListener('keydown', e => {
            if (e.key === "Escape") {
                hideViewer();
                closeImageModal();
                closeConfigModal();
            }
        });
        let zoomInitialized = false;

        function initializeZoomFeature() {
            // 避免重复初始化
            if (zoomInitialized) return;
            
            const setupPaneZoom = (pane) => {
                // 移除之前的事件监听器（如果有）
                pane.removeEventListener('mouseenter', pane._zoomEnterHandler);
                pane.removeEventListener('mouseleave', pane._zoomLeaveHandler);
                pane.removeEventListener('mousemove', pane._zoomMoveHandler);
                
                // 创建新的事件处理器
                pane._zoomEnterHandler = () => {
                    const img = pane.querySelector('img');
                    if (img && img.src && img.complete && img.naturalHeight !== 0) {
                        pane.classList.add('zoomed');
                    }
                };

                pane._zoomLeaveHandler = () => {
                    const img = pane.querySelector('img');
                    pane.classList.remove('zoomed');
                    if (img) {
                        img.style.transformOrigin = 'center center';
                    }
                };

                pane._zoomMoveHandler = (e) => {
                    const img = pane.querySelector('img');
                    if (!pane.classList.contains('zoomed') || !img) return;

                    const rect = pane.getBoundingClientRect();
                    const x = e.clientX - rect.left;
                    const y = e.clientY - rect.top;

                    const xPercent = (x / rect.width) * 100;
                    const yPercent = (y / rect.height) * 100;

                    img.style.transformOrigin = `${xPercent}% ${yPercent}%`;
                };
                
                // 添加事件监听器
                pane.addEventListener('mouseenter', pane._zoomEnterHandler);
                pane.addEventListener('mouseleave', pane._zoomLeaveHandler);
                pane.addEventListener('mousemove', pane._zoomMoveHandler);
            };

            setupPaneZoom($('#apiPane'));
            setupPaneZoom($('#localPane'));
            zoomInitialized = true;
        }

        function reinitializeZoomForModal() {
            // 为模态框重新初始化缩放功能
            const apiPane = $('#apiPane');
            const localPane = $('#localPane');
            
            if (apiPane && localPane) {
                const setupPaneZoom = (pane) => {
                    // 移除之前的事件监听器
                    const oldEnter = pane._modalZoomEnterHandler;
                    const oldLeave = pane._modalZoomLeaveHandler;
                    const oldMove = pane._modalZoomMoveHandler;
                    
                    if (oldEnter) pane.removeEventListener('mouseenter', oldEnter);
                    if (oldLeave) pane.removeEventListener('mouseleave', oldLeave);
                    if (oldMove) pane.removeEventListener('mousemove', oldMove);
                    
                    // 创建新的事件处理器
                    pane._modalZoomEnterHandler = () => {
                        const img = pane.querySelector('img');
                        if (img && img.src && img.complete && img.naturalHeight !== 0) {
                            pane.classList.add('zoomed');
                        }
                    };

                    pane._modalZoomLeaveHandler = () => {
                        const img = pane.querySelector('img');
                        pane.classList.remove('zoomed');
                        if (img) {
                            img.style.transformOrigin = 'center center';
                        }
                    };

                    pane._modalZoomMoveHandler = (e) => {
                        const img = pane.querySelector('img');
                        if (!pane.classList.contains('zoomed') || !img) return;

                        const rect = pane.getBoundingClientRect();
                        const x = e.clientX - rect.left;
                        const y = e.clientY - rect.top;

                        const xPercent = (x / rect.width) * 100;
                        const yPercent = (y / rect.height) * 100;

                        img.style.transformOrigin = `${xPercent}% ${yPercent}%`;
                    };
                    
                    // 添加事件监听器
                    pane.addEventListener('mouseenter', pane._modalZoomEnterHandler);
                    pane.addEventListener('mouseleave', pane._modalZoomLeaveHandler);
                    pane.addEventListener('mousemove', pane._modalZoomMoveHandler);
                };

                setupPaneZoom(apiPane);
                setupPaneZoom(localPane);
            }
        }

        document.addEventListener('DOMContentLoaded', () => {
            addLog('商品数据提取工具已就绪', 'info');
            initializeZoomFeature();

            // 建立实时日志连接
            connectToLogStream();

            // SKU输入框回车事件
            $('#skuInput').addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    searchBySKU();
                }
            });

            // 加载搜索配置
            loadSearchConfig();

            // 配置选项变更事件监听
            setupConfigListeners();

            // 页面卸载时关闭连接
            window.addEventListener('beforeunload', () => {
                if (eventSource) {
                    eventSource.close();
                }
            });
        });
    </script>
</body>
</html>